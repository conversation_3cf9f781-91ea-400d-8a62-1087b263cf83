{"rustc": 524190467255570058, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 12206360443249279867, "path": 8779349228435262488, "deps": [[6229549618243515530, "windows_link", false, 9003413152115529319], [10448766010662481490, "num_traits", false, 6195993361681421827], [10633404241517405153, "serde", false, 13407945755440213337]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-6a8d1e7ed5dfcb6d\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 0}