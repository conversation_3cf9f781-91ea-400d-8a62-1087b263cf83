import { useState, useEffect } from 'react'
import {
  Theme<PERSON>rovider,
  createTheme,
  CssBaseline,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  Container,
  Button,
  Box,
  Alert,
  IconButton
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Add as AddIcon,
  Close as CloseIcon,
  SportsEsports as GameIcon
} from '@mui/icons-material'
import ServerList from './components/ServerList'
import AddServerForm from './components/AddServerForm'
import { fetchServers, queryAllServers } from './services/api'

// Create dark theme
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#4CAF50',
    },
    secondary: {
      main: '#2196F3',
    },
    background: {
      default: '#0a0a0a',
      paper: '#1a1a1a',
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
        },
      },
    },
  },
})

function App() {
  const [servers, setServers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Load servers on component mount
  useEffect(() => {
    loadServers()
  }, [])

  const loadServers = async () => {
    try {
      setLoading(true)
      setError(null)
      const serverData = await fetchServers()
      setServers(serverData)
    } catch (err) {
      setError('Failed to load servers: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleServerAdded = () => {
    setShowAddForm(false)
    loadServers() // Refresh the server list
  }

  const handleRefreshAll = async () => {
    try {
      setRefreshing(true)
      await queryAllServers()
      await loadServers() // Reload to get updated data
    } catch (err) {
      setError('Failed to refresh servers: ' + err.message)
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static" elevation={0}>
          <Toolbar>
            <GameIcon sx={{ mr: 2 }} />
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              QuakeWorld Server Browser
            </Typography>
            <Button
              color="inherit"
              startIcon={refreshing ? <RefreshIcon className="spinning" /> : <RefreshIcon />}
              onClick={handleRefreshAll}
              disabled={refreshing}
              sx={{ mr: 1 }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh All'}
            </Button>
            <Button
              color="inherit"
              startIcon={showAddForm ? <CloseIcon /> : <AddIcon />}
              onClick={() => setShowAddForm(!showAddForm)}
            >
              {showAddForm ? 'Cancel' : 'Add Server'}
            </Button>
          </Toolbar>
        </AppBar>

        <Container maxWidth="xl" sx={{ mt: 2, mb: 2 }}>
          {error && (
            <Alert
              severity="error"
              sx={{ mb: 2 }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => setError(null)}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
            >
              {error}
            </Alert>
          )}

          {showAddForm && (
            <Box sx={{ mb: 3 }}>
              <AddServerForm
                onServerAdded={handleServerAdded}
                onCancel={() => setShowAddForm(false)}
              />
            </Box>
          )}

          <ServerList
            servers={servers}
            loading={loading}
            onServerUpdated={loadServers}
          />
        </Container>
      </Box>
    </ThemeProvider>
  )
}

export default App
