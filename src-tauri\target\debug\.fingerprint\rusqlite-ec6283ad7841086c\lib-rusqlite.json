{"rustc": 524190467255570058, "features": "[\"bundled\", \"chrono\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 11514069381963231523, "profile": 12206360443249279867, "path": 8458331605581248826, "deps": [[1001987408463263701, "fallible_streaming_iterator", false, 4210277188426717551], [1782259520642170691, "chrono", false, 7569573158045597167], [3769079237034864193, "hashlink", false, 5680681546496261526], [4722402946104583944, "bitflags", false, 2475063479964976522], [13902819013840624958, "smallvec", false, 4642040453273627471], [14142233521602316891, "libsqlite3_sys", false, 16600084136449756706], [18440888379705904486, "fallible_iterator", false, 10555191968306735970]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-ec6283ad7841086c\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "metadata": 10818493136143991971, "config": 2202906307356721367, "compile_kind": 0}