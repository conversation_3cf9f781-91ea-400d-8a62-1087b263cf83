{"rustc": 524190467255570058, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12399645807183089016, "build_script_build", false, 355231581490834025], [3673018343088348276, "build_script_build", false, 7893769596467322899], [10013554210477138623, "build_script_build", false, 16878779019738442231]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-6a1c24be960bb742\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}