import { useState } from 'react'
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Add as AddIcon,
  Science as TestIcon,
  Cancel as CancelIcon
} from '@mui/icons-material'
import { addServer, testServer } from '../services/api'

const AddServerForm = ({ onServerAdded, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    port: '27500'
  })
  const [testing, setTesting] = useState(false)
  const [adding, setAdding] = useState(false)
  const [testResult, setTestResult] = useState(null)
  const [error, setError] = useState(null)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear previous test result when form changes
    setTestResult(null)
    setError(null)
  }

  const handleTest = async (e) => {
    e.preventDefault()

    if (!formData.address || !formData.port) {
      setError('Address and port are required')
      return
    }

    try {
      setTesting(true)
      setError(null)
      const result = await testServer(formData.address, parseInt(formData.port))
      setTestResult(result)

      // Auto-fill server name if not provided and test was successful
      if (!formData.name && result.success && result.serverInfo?.hostname) {
        setFormData(prev => ({
          ...prev,
          name: result.serverInfo.hostname
        }))
      }
    } catch (err) {
      setError('Test failed: ' + err.message)
      setTestResult(null)
    } finally {
      setTesting(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.name || !formData.address || !formData.port) {
      setError('All fields are required')
      return
    }

    try {
      setAdding(true)
      setError(null)
      await addServer({
        name: formData.name,
        address: formData.address,
        port: parseInt(formData.port)
      })
      onServerAdded()
    } catch (err) {
      setError('Failed to add server: ' + err.message)
    } finally {
      setAdding(false)
    }
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Add New Server
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Server Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="My QuakeWorld Server"
              required
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label="Address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              placeholder="qw.example.com"
              required
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Port"
              name="port"
              type="number"
              value={formData.port}
              onChange={handleInputChange}
              inputProps={{ min: 1, max: 65535 }}
              required
              size="small"
            />
          </Grid>
        </Grid>

        {testResult && (
          <Alert
            severity={testResult.success ? 'success' : 'error'}
            sx={{ mt: 2 }}
          >
            {testResult.success ? (
              <Box>
                <Typography variant="body2" gutterBottom>
                  ✅ Server is online!
                </Typography>
                <Box display="flex" gap={2} flexWrap="wrap">
                  <Typography variant="caption">
                    Ping: {testResult.ping}ms
                  </Typography>
                  {testResult.playerCount !== undefined && (
                    <Typography variant="caption">
                      Players: {testResult.playerCount}/{testResult.maxPlayers}
                    </Typography>
                  )}
                  {testResult.mapName && (
                    <Typography variant="caption">
                      Map: {testResult.mapName}
                    </Typography>
                  )}
                </Box>
              </Box>
            ) : (
              <Typography variant="body2">
                ❌ Server is offline or unreachable
              </Typography>
            )}
          </Alert>
        )}

        <Box display="flex" gap={1} justifyContent="flex-end" mt={3}>
          <Button
            variant="outlined"
            startIcon={testing ? <CircularProgress size={16} /> : <TestIcon />}
            onClick={handleTest}
            disabled={testing || !formData.address || !formData.port}
          >
            {testing ? 'Testing...' : 'Test Connection'}
          </Button>

          <Button
            variant="outlined"
            startIcon={<CancelIcon />}
            onClick={onCancel}
          >
            Cancel
          </Button>

          <Button
            type="submit"
            variant="contained"
            startIcon={adding ? <CircularProgress size={16} /> : <AddIcon />}
            disabled={adding}
          >
            {adding ? 'Adding...' : 'Add Server'}
          </Button>
        </Box>
      </Box>
    </Paper>
  )
}

export default AddServerForm
