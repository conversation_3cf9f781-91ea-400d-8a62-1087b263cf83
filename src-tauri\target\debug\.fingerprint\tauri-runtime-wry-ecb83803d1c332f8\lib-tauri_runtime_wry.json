{"rustc": 524190467255570058, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1729391596725106306, "profile": 12206360443249279867, "path": 2073769788179129543, "deps": [[3866996022237765862, "tauri_runtime", false, 17515103974135220507], [5720063691501171767, "tauri_utils", false, 14562234223738653880], [6410343819635645113, "raw_window_handle", false, 16402341225296434576], [7269961468903682264, "tao", false, 5250296823515129490], [8244776183334334055, "once_cell", false, 17250952856057021305], [12368485582061518913, "http", false, 17942408560271028670], [13957640150707709086, "build_script_build", false, 15530941469684223474], [14989576828835817845, "wry", false, 13481384135685774457], [15399619262696441677, "log", false, 1215765798464921259], [16148844073572089219, "webview2_com", false, 12420882960695032655], [16731215249320019310, "softbuffer", false, 452878000534753974], [18102972475842925027, "windows", false, 3540019114950597070], [18130989770956114225, "url", false, 14884025751106632641]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ecb83803d1c332f8\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "metadata": 6527887627390916949, "config": 2202906307356721367, "compile_kind": 0}