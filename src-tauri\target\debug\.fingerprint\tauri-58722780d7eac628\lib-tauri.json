{"rustc": 524190467255570058, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 8088998362596673410, "profile": 12206360443249279867, "path": 13831012499639318720, "deps": [[265113383264221167, "url<PERSON><PERSON>n", false, 18286782121933444483], [2070739116102306658, "tokio", false, 14774239915716791694], [2253645963862362999, "glob", false, 8741446517217925875], [3673018343088348276, "build_script_build", false, 7893769596467322899], [3866996022237765862, "tauri_runtime", false, 17515103974135220507], [3930354675071354477, "percent_encoding", false, 15416648424419641516], [5720063691501171767, "tauri_utils", false, 14562234223738653880], [6410343819635645113, "raw_window_handle", false, 16402341225296434576], [7470442545028885647, "mime", false, 16829204539868928463], [8670877025618955261, "window_vibrancy", false, 8699570456885876185], [8678281613339888692, "dunce", false, 2333239884148260758], [9343801854649291285, "serde_repr", false, 2974105413572281885], [10291739091677281249, "anyhow", false, 1464287507274173904], [10633404241517405153, "serde", false, 13407945755440213337], [11904832810386201650, "tauri_macros", false, 12875761195128916338], [12368485582061518913, "http", false, 17942408560271028670], [12509852874546367857, "serde_json", false, 3689103298446316903], [12779436558119558850, "getrandom", false, 12644390013427092467], [12809794537881357198, "thiserror", false, 17229314362348417684], [13678960081484093953, "serialize_to_javascript", false, 2237930891567516973], [13899702929860959449, "dirs", false, 13589775137444623565], [13957640150707709086, "tauri_runtime_wry", false, 14075933996381824863], [15399619262696441677, "log", false, 1215765798464921259], [16148844073572089219, "webview2_com", false, 12420882960695032655], [16476303074998891276, "futures_util", false, 93859681573989099], [16933062573314061224, "muda", false, 15598465860517449453], [17175234422038868540, "heck", false, 5115222869234866468], [18102972475842925027, "windows", false, 3540019114950597070], [18130989770956114225, "url", false, 14884025751106632641]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-58722780d7eac628\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "metadata": 8230821343879830340, "config": 2202906307356721367, "compile_kind": 0}