{"rustc": 524190467255570058, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 3865210990427632890, "profile": 12206360443249279867, "path": 2084711048383712332, "deps": [[3269169667440160398, "cookie", false, 9936401600302138930], [3866996022237765862, "build_script_build", false, 1306989683415386924], [5390965438110126853, "dpi", false, 15134313980571444159], [5720063691501171767, "tauri_utils", false, 14562234223738653880], [6410343819635645113, "raw_window_handle", false, 16402341225296434576], [10633404241517405153, "serde", false, 13407945755440213337], [12368485582061518913, "http", false, 17942408560271028670], [12509852874546367857, "serde_json", false, 3689103298446316903], [12809794537881357198, "thiserror", false, 17229314362348417684], [18102972475842925027, "windows", false, 3540019114950597070], [18130989770956114225, "url", false, 14884025751106632641]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-ebe89c39a59eca92\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "metadata": 12594724888323783112, "config": 2202906307356721367, "compile_kind": 0}