{"rustc": 524190467255570058, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 9876947139072873629, "profile": 12206360443249279867, "path": 13981670381935741583, "deps": [[14142233521602316891, "build_script_build", false, 13877100533538999918]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-ca03192ab20a6d8e\\dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "metadata": 14430067081211154946, "config": 2202906307356721367, "compile_kind": 0}