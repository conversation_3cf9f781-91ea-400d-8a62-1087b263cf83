{"rustc": 524190467255570058, "features": "[]", "declared_features": "[]", "target": 18164180632043058070, "profile": 11597332650809196192, "path": 10602529704205407992, "deps": [[1782259520642170691, "chrono", false, 7569573158045597167], [2070739116102306658, "tokio", false, 14774239915716791694], [3263337411901553356, "rusqlite", false, 16997324265089775414], [3673018343088348276, "tauri", false, 14614633142838674871], [9838636245637313259, "uuid", false, 1749963720834253021], [10013554210477138623, "tauri_plugin_log", false, 11409765045705678518], [10291739091677281249, "anyhow", false, 1464287507274173904], [10633404241517405153, "serde", false, 13407945755440213337], [12399645807183089016, "app_lib", false, 9141307239190339788], [12399645807183089016, "build_script_build", false, 14062097724862154212], [12509852874546367857, "serde_json", false, 3689103298446316903], [15399619262696441677, "log", false, 1215765798464921259]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\app-d7b31eb4c41ad7de\\dep-bin-app", "checksum": false}}], "rustflags": [], "metadata": 7513903081705498502, "config": 2202906307356721367, "compile_kind": 0}