{"rustc": 524190467255570058, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 7890680933942864499, "profile": 13232757476167777671, "path": 464900876267886465, "deps": [[5720063691501171767, "tauri_utils", false, 4534094580039334739], [9985077643059611075, "syn", false, 6259024649005329609], [12264309469320276423, "tauri_codegen", false, 5651138904956973675], [17175234422038868540, "heck", false, 5115222869234866468], [17525013869477438691, "quote", false, 4886547776623494846], [18036439996138669183, "proc_macro2", false, 10189828033789376292]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-815f68ae09d2be50\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "metadata": 2550541321619233985, "config": 2202906307356721367, "compile_kind": 0}